<?php

use App\Http\Controllers\Admin\TestimonialController;
use Illuminate\Support\Facades\Route;
use App\V2\School\Http\Controllers\{
    Auth<PERSON>ontroller,
    MessageController,
    RequirementsController,
    CalculateBudgetController,
};


#region Open Routes
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
], function () {
    Route::post('login', [AuthController::class, 'login'])->name('login');


    Route::group([
        'prefix' => 'password',
        'as' => 'password.',
    ], function () {
        Route::get('check', [AuthController::class, 'checkResetRequired'])->name('checkResetRequired');
        Route::post('reset', [AuthController::class, 'resetPassword'])->name('resetPassword');
    });

    Route::group([
        'prefix' => 'otp',
        'as' => 'otp.',
    ], function () {
        Route::post('send', [AuthController::class, 'sendOtp'])->name('send');
        Route::post('resend', [AuthController::class, 'resendOtp'])->name('resend');
        Route::post('verify', [AuthController::class, 'verifyOtp'])->name('verify');
        Route::post('reset-password', [AuthController::class, 'resetPasswordWithOtp'])->name('reset_password');
    });
});

#region Auth Required
Route::group([
    'prefix' => 'schools',
    'as' => 'school.',
    'middleware' => ['CheckSchoolSession', 'auth:platform_school'],
], function () {
    // User Profile
    Route::get("logout", [AuthController::class, "sessionLogout"])->name('sessionLogout');
    Route::get('profile', [AuthController::class, 'profile'])->name('profile');
    Route::get('testimonial', [TestimonialController::class, 'getAllTestimonial'])->name('testimonial');

    #region Calculate budget
    Route::get('subject-budgets/{subjectId}', [CalculateBudgetController::class, 'bySubject'])->name('getAllBudgets');
    Route::post('store-budgets', [CalculateBudgetController::class, 'store'])->name('storeBudgets');
    Route::post('update-budgets', [CalculateBudgetController::class, 'update'])->name('updateBudgets');
    Route::post('delete-budgets', [CalculateBudgetController::class, 'destroy'])->name('deleteBudgets');
    Route::post('calculate-budgets', [CalculateBudgetController::class, 'calculateBudget'])->name('calculateBudget');
    Route::get('all-budgets', [CalculateBudgetController::class, 'fetchAllSchoolBudget'])->name('fetchAllSchoolBudget');
    Route::group([
        'prefix' => 'calculate-budgets',
        'as' => 'calculate_budgets.',
    ], function () {
        Route::apiResource('', CalculateBudgetController::class);
        Route::get('{id}/duplicate', [CalculateBudgetController::class, 'duplicate_budget'])->name('duplicate');
    });
    #endregion

    #region Post Requirement
    Route::group([
        'prefix' => 'requirements',
        'as' => 'requirements.',
    ], function () {
        Route::apiResource('', RequirementsController::class);
        Route::get('{id}/requirement', [RequirementsController::class, 'getRequirementById'])->name('requirement');
        Route::get('{id}/educator', [RequirementsController::class, 'getEducatorById'])->name('educator');
        Route::get('utils', [RequirementsController::class, 'getPostUtilities'])->name('utils');
        Route::post('store', [RequirementsController::class, 'store'])->name('store');
        Route::get('{id}/duplicate', [RequirementsController::class, 'duplicateRequirement'])->name('duplicate');
        Route::get('{id}/close', [RequirementsController::class, 'closeRequirement'])->name('close');
        Route::get('{id}/delete', [RequirementsController::class, 'delete'])->name('delete');
        Route::get('{id}/review-applicants', [RequirementsController::class, 'reviewApplicantList'])->name('review-applicants');
        Route::post('like-dislike-applicant', [RequirementsController::class, 'likeDislikeApplicant'])->name('like-dislike-applicant');
        Route::get('get-applicant', [RequirementsController::class, 'getEligibleApplicant'])->name('get-applicant');
        Route::post('invite-applicant', [RequirementsController::class, 'inviteApplicant'])->name('invite-applicant');
        Route::get('invite-history', [RequirementsController::class, 'inviteHistory'])->name('invite-history');
        Route::post('withdraw-invite/{id}', [RequirementsController::class, 'withdrawInvite'])->name('withdraw-invite');

        // Post Requirements
        Route::get('all-post-requirements', [RequirementsController::class, 'getAllPostRequirements'])->name('all-post-requirements');
        Route::get('post-requirement/{id}', [RequirementsController::class, 'getPostRequirementDetails'])->name('post-requirement-details');
    });
    #endregion



    #region Messages
    Route::group([
        'prefix' => 'messages',
        'as' => 'message.',
    ], function () {
        Route::get('', [MessageController::class, 'index'])->name('index');
        Route::get('{id}', [MessageController::class, 'show'])->name('show');
        Route::delete('{id}', [MessageController::class, 'destroy'])->name('destroy');
    });
    #endregion
});