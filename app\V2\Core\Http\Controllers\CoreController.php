<?php

namespace App\V2\Core\Http\Controllers;

use DB;
use Validator;
use Exception;
use App\Classes;
use App\StateModel;
use App\OnboardingInstructor;
use App\FeaturedEducatorMatrices;
use App\Models\k12ConnectionCategorizedData;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use App\V2\Core\Helpers\ApiResponse;

class CoreController extends Controller
{
    /**
     * Get category-based info from school_management_setting
     * requirement_for | position_type | certificate | language | profile_type | per_hour_range | grades | states | program_type | timezone
     */
    public function getCategoryBasedInfo($type)
    {
        try {
            if($type == 'grades') {
                $grades = Classes::all();
                return ApiResponse::success(
                    $grades,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'states') {
                $states = StateModel::where(["country_id" => "239"])->where("id", "!=", "60")->get();
                return ApiResponse::success(
                    $states,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'program_type') {
                $program_type = k12ConnectionCategorizedData::where('type', 'program_type')->get();
                return ApiResponse::success(
                    $program_type,
                    ucfirst($type) . " retrieved successfully."
                );
            } elseif ($type == 'timezone') {
                $timezone = k12ConnectionCategorizedData::where('type', 'timezone')->get();
                return ApiResponse::success(
                    $timezone,
                    ucfirst($type) . " retrieved successfully."
                );
            }
            // requirement_for | position_type | certificate | language | profile_type | per_hour_range
            $record = DB::table('school_management_setting')
                ->where('type', $type)
                ->first();

            if (!$record) {
                return ApiResponse::error(
                    ucfirst($type) . " not found.",
                    404
                );
            }

            $values = json_decode($record->value, true);

            if (json_last_error() !== JSON_ERROR_NONE) {

                return ApiResponse::error(
                    "Data for {$type} is corrupted or invalid.",
                    500
                );
            }

            return ApiResponse::success(
                $values,
                ucfirst($type) . " retrieved successfully."
            );
        } catch (QueryException $e) {
            // Return a 500 server error response with a user-friendly message
            return ApiResponse::error('A database error occurred while saving the budget.', 500, $e);
        } catch (Exception $e) {

            return ApiResponse::error(
                "Something went wrong while retrieving {$type}.",
                500,
                config('app.debug') ? [$e->getMessage()] : []
            );
        }
    }

    // ***************Featured-Educator***************
    public function getFeaturedEducator() {
        try {
            $featuredEducatorMatrices = FeaturedEducatorMatrices::with(['educator.step1', 'educator.step2.education', 'educator.step2.teching', 'educator.step3.subjects.subSubject.subjectArea', 'educator.step5', 'educator.step6'])
                ->whereHas('educator', function($query) {
                    $query->whereIn('user_status', ['Active']);
                })->orderBy('weighted_score', 'desc')->get();

            // Map the featured educators from matrices
            $educators = $featuredEducatorMatrices->map(function ($matrix) {
                $educator = $matrix->educator;
                $subjects = [];

                if ($educator->step3 && $educator->step3->subjects) {
                    foreach ($educator->step3->subjects as $subject) {
                        $subjectData = [
                            'subject_area_name' => v1SubjectAreaName($subject->subject),
                            'sub_subject_name' => v1SubjectName($subject->sub_subject),
                            'proficiency' => $subject->proficiency,
                        ];
                        $subjects[] = $subjectData;
                    }
                }

                return [
                    'id' => $educator->id,
                    'title' => $educator->step5 ? $educator->step5->profile_title : null,
                    'first_name' => $educator->first_name,
                    'last_name' => $educator->last_name,
                    'image' => $educator->image ? generateSignedUrl($educator->image) : null,
                    'subject' => $subjects,
                    'total_classes_taught' => $matrix->class_count,
                    'weighted_score' => $matrix->weighted_score,
                    'program_count' => $matrix->program_count,
                    'class_count' => $matrix->class_count,
                    'avg_rating' => $matrix->avg_rating,
                    'lifetime_avg_rating' => $matrix->lifetime_avg_rating,
                    'featured_period' => [
                        'start_date' => $matrix->start_date,
                        'end_date' => $matrix->end_date,
                    ],
                ];
            });

            return ApiResponse::success($educators, "Featured educators fetched successfully");
        } catch (Exception $e) {
            return ApiResponse::error("Failed to fetch featured educators: " . $e->getMessage(), 500);
        }
    }
    // ***************Featured-Educator***************

    public function getS3Url(Request $request) {
    // Step 1: Validate input
        $validator = Validator::make($request->all(), [
            'filePath' => 'required|string',
        ]);

        if ($validator->fails()) {
            return ApiResponse::error('Invalid input.', $validator->errors());
        }

        try {
            // Step 2: Check if the file exists in S3 (optional but recommended)
            $filePath = $request->input('filePath');

            if (!Storage::disk('s3')->exists($filePath)) {
                return ApiResponse::error('File not found on S3.', [], 404);
            }

            // Step 3: Generate the signed URL
            $url = Storage::disk('s3')->temporaryUrl(
                $filePath,
                now()->addMinutes(60)
            );

            return ApiResponse::success($url, "Url Fetched Successfully");

        } catch (\Exception $e) {
            return ApiResponse::error('Failed to generate signed URL.', $e->getMessage());
        }
    }
}
